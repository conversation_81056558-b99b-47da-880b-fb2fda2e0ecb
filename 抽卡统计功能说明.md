# 鸣潮抽卡统计功能

## 功能概述

当用户发送抽卡记录命令后，系统会自动获取用户的QQ号、群号、游戏内昵称、UID以及抽卡统计数据，并保存到数据库中。

## 数据库表结构

### WavesGachaStats 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键，自增 |
| user_id | str | 用户QQ号 |
| bot_id | str | 机器人ID |
| group_id | str | 群号（可选） |
| nickname | str | 游戏内昵称（可选） |
| uid | str | 鸣潮UID |
| char_avg_gold | float | 角色精准调谐平均出金抽数 |
| char_avg_up | float | 角色精准调谐平均UP抽数 |
| char_total_pulls | int | 角色精准调谐总抽数 |
| weapon_avg_gold | float | 武器精准调谐平均出金抽数 |
| weapon_avg_up | float | 武器精准调谐平均UP抽数 |
| weapon_total_pulls | int | 武器精准调谐总抽数 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 修改的文件

### 1. `utils/database/models.py`
- 添加了 `WavesGachaStats` 数据库模型
- 添加了 `save_or_update_stats` 方法用于保存或更新统计数据
- 添加了 `get_user_stats`、`get_group_stats`、`get_all_stats` 查询方法
- 添加了管理后台配置 `WavesGachaStatsAdmin`

### 2. `wutheringwaves_gachalog/draw_gachalogs.py`
- 在 `draw_card` 函数中添加了调用 `save_gacha_stats_to_db` 的逻辑
- 新增了 `save_gacha_stats_to_db` 函数，用于：
  - 获取游戏内昵称（通过API调用）
  - 提取角色池和武器池的统计数据
  - 保存数据到数据库

## 功能特点

### 1. 自动获取游戏内昵称
- 通过调用游戏API获取真实的游戏内昵称
- 如果API调用失败，昵称字段会为空，不影响其他功能

### 2. 数据去重和更新
- 基于 `user_id`、`bot_id`、`uid` 的组合进行去重
- 如果记录已存在，会更新现有记录而不是创建新记录

### 3. 错误处理
- 数据库操作失败不会影响抽卡记录的正常显示
- 所有异常都会被捕获并记录

### 4. 数据完整性
- 支持角色池和武器池的完整统计
- 包含平均出金、平均UP、总抽数等关键指标

## 使用方法

用户正常发送抽卡记录命令，系统会自动在后台保存统计数据，无需额外操作。

## 管理功能

通过管理后台可以查看和管理所有的抽卡统计数据：
- 查看用户抽卡统计
- 查看群组排行
- 数据导出等功能

## 注意事项

1. 游戏内昵称获取需要有效的Cookie
2. 数据库操作是异步的，不会阻塞主要功能
3. 统计数据会在每次查看抽卡记录时更新
4. 支持多个UID的用户，每个UID都会有独立的统计记录
