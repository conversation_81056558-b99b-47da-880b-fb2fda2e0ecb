#!/usr/bin/env python3
"""
测试图片资源加载的脚本
"""

import sys
from pathlib import Path

# 添加项目路径到 sys.path
project_root = Path(__file__).parent / "WutheringWavesUID" / "WutheringWavesUID"
sys.path.insert(0, str(project_root))

def test_image_resources():
    """测试所有图片资源的加载"""
    try:
        print("🔍 测试图片资源加载...")
        
        from PIL import Image
        
        # 图片资源路径
        texture_path = project_root / "wutheringwaves_rank" / "texture2d"
        
        # 需要测试的图片资源
        required_images = {
            "avatar_mask.png": "头像遮罩",
            "avatar_ring.png": "头像装饰环",
            "logo_small_2.png": "Logo图标",
            "bar1.png": "条目背景",
            "title.png": "标题装饰",
            "promote_icon.png": "特殊标识图标",
            "score_sss.png": "SSS等级图标",
            "score_ss.png": "SS等级图标", 
            "score_s.png": "S等级图标",
            "score_a.png": "A等级图标",
            "score_b.png": "B等级图标",
            "score_c.png": "C等级图标"
        }
        
        print(f"纹理路径: {texture_path}")
        print(f"需要测试 {len(required_images)} 个图片资源\n")
        
        loaded_images = {}
        missing_images = []
        
        # 测试每个图片文件
        for filename, description in required_images.items():
            file_path = texture_path / filename
            
            if file_path.exists():
                try:
                    img = Image.open(file_path)
                    loaded_images[filename] = {
                        "image": img,
                        "size": img.size,
                        "mode": img.mode,
                        "description": description
                    }
                    print(f"✅ {filename:20} - {description:15} - 尺寸: {img.size} - 模式: {img.mode}")
                except Exception as e:
                    print(f"❌ {filename:20} - 加载失败: {e}")
                    missing_images.append(filename)
            else:
                print(f"❌ {filename:20} - 文件不存在")
                missing_images.append(filename)
        
        # 统计结果
        success_count = len(loaded_images)
        total_count = len(required_images)
        
        print(f"\n📊 加载结果统计:")
        print(f"成功加载: {success_count}/{total_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        if missing_images:
            print(f"\n⚠️ 缺失的图片文件:")
            for img in missing_images:
                print(f"  - {img}")
        
        # 测试运气等级系统
        print(f"\n🎯 运气等级系统测试:")
        test_avg_golds = [35, 45, 55, 65, 75, 85]
        level_names = ["SSS", "SS", "S", "A", "B", "C"]
        
        for avg_gold in test_avg_golds:
            if avg_gold <= 40:
                level = "SSS"
                icon_file = "score_sss.png"
            elif avg_gold <= 50:
                level = "SS"
                icon_file = "score_ss.png"
            elif avg_gold <= 60:
                level = "S"
                icon_file = "score_s.png"
            elif avg_gold <= 70:
                level = "A"
                icon_file = "score_a.png"
            elif avg_gold <= 80:
                level = "B"
                icon_file = "score_b.png"
            else:
                level = "C"
                icon_file = "score_c.png"
            
            icon_status = "✅" if icon_file in loaded_images else "❌"
            print(f"  平均{avg_gold}抽 -> {level}级 {icon_status}")
        
        # 测试图片尺寸适配
        print(f"\n📐 图片尺寸适配测试:")
        size_tests = {
            "avatar_ring.png": (80, 80),
            "title.png": (400, "自适应"),
            "promote_icon.png": (40, 40),
            "score_sss.png": (60, 30)
        }
        
        for filename, target_size in size_tests.items():
            if filename in loaded_images:
                original_size = loaded_images[filename]["size"]
                if isinstance(target_size[1], str):
                    print(f"  {filename:20} - 原始: {original_size} -> 目标: {target_size}")
                else:
                    print(f"  {filename:20} - 原始: {original_size} -> 目标: {target_size}")
            else:
                print(f"  {filename:20} - 文件缺失，无法测试")
        
        return success_count == total_count
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_layout_positions():
    """测试新的布局位置"""
    print("\n📍 新增元素布局位置:")
    
    layout_info = {
        "标题装饰": {
            "位置": "(400, 20)",
            "大小": "自适应宽度400px",
            "说明": "标题区域装饰图片"
        },
        "头像装饰环": {
            "位置": "头像位置覆盖",
            "大小": "80x80",
            "说明": "头像装饰边框"
        },
        "运气等级图标": {
            "位置": "(950, 35)",
            "大小": "60x30", 
            "说明": "替代原文字运气标签"
        },
        "当前用户标识": {
            "位置": "(1050, 40)",
            "大小": "40x40",
            "说明": "当前用户专用特殊图标"
        }
    }
    
    for element, info in layout_info.items():
        print(f"  {element:12}: {info['位置']:15} - {info['大小']:10} - {info['说明']}")
    
    print(f"\n🎨 视觉效果增强:")
    print("  - 标题区域更加丰富和层次化")
    print("  - 头像具有精美的装饰边框")
    print("  - 运气等级使用直观的图标系统")
    print("  - 当前用户有明确的视觉标识")
    print("  - 整体保持游戏风格的一致性")

if __name__ == "__main__":
    print("🧪 图片资源优化测试\n")
    
    # 测试图片资源加载
    resource_ok = test_image_resources()
    
    # 测试布局位置
    test_layout_positions()
    
    print(f"\n📋 测试结果:")
    print(f"图片资源: {'✅ 全部加载成功' if resource_ok else '❌ 部分资源缺失'}")
    
    if resource_ok:
        print("\n🎉 所有图片资源测试通过！抽卡排行榜的视觉效果将大幅提升！")
        print("\n🎯 新功能特点:")
        print("  ✨ 游戏风格的装饰元素")
        print("  🏆 直观的运气等级系统")
        print("  👤 明确的当前用户标识")
        print("  🎨 丰富的视觉层次效果")
    else:
        print("\n⚠️ 部分图片资源缺失，请检查 texture2d 文件夹！")
