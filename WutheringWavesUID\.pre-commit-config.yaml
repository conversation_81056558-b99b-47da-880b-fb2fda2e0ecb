ci:
  autofix_commit_msg: "🚨 `pre-commit-ci`修复格式错误"
  # skip:
  #   - "poetry-lock"
  autofix_prs: true
  autoupdate_branch: v4
  autoupdate_schedule: monthly
  autoupdate_commit_msg: "⬆️ `pre-commit-ci`自动升级"
repos:
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

  - repo: https://github.com/hadialqattan/pycln
    rev: v2.1.3
    hooks:
      - id: pycln

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: "v0.1.2"
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
