<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>岸宝</title>
  <link rel="icon" href="https://web-static.kurobbs.com/resource/prod/icon.ico" type="image/x-icon">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f0f0f0;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      padding: 0;
    }

    .container {
      text-align: center;
      background-color: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 90%;
      width: 400px;
    }

    h1 {
      font-size: 4rem;
      margin: 0;
      color: #e74c3c;
    }

    p {
      font-size: 1.2rem;
      margin: 1rem 0;
    }

    a {
      display: inline-block;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      transition: background-color 0.3s ease;
    }

    a:hover {
      background-color: #2980b9;
    }

    /* 模态窗口样式 */
    .modal {
      display: none;
      justify-content: center;
      align-items: center;
      position: fixed;
      z-index: 999;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      overflow: auto;
      background-color: rgba(0,0,0,0.4);
    }

    .modal.show {
      display: flex;
    }

    .modal-content {
      display: block;
      max-width: 90vw;
      max-height: 80vh;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      margin: 0;
    }

    .close {
      position: absolute;
      right: 30px;
      top: 20px;
      color: #fff;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      z-index: 1000;
    }
  </style>
</head>
<body>
<div class="container">
  <h1>登录过期</h1>
  <p>请重新发起登录请求</p>
  <a id="showModalBtn" href="javascript:void(0);">岸宝拉群</a> 
</div>

<!-- 模态窗口结构 -->
<div id="myModal" class="modal">
  <span class="close">&times;</span>
  <img class="modal-content" id="modalImg" src="https://bbs.ansey.xyz/img/%E5%B2%B8%E5%AE%9D%E7%A7%9F%E7%94%A8.png" alt="机器人租用">
</div>

<script>
  // 获取元素
  var modal = document.getElementById("myModal");
  var btn = document.getElementById("showModalBtn");
  var span = document.getElementsByClassName("close")[0];

  // 点击按钮显示模态窗口
  btn.onclick = function() {
    modal.classList.add("show");
  }

  // 点击关闭按钮关闭模态窗口
  span.onclick = function() {
    modal.classList.remove("show");
  }

  // 点击模态窗口外部关闭
  window.onclick = function(event) {
    if (event.target == modal) {
      modal.classList.remove("show");
    }
  }
</script>
</body>
</html>
