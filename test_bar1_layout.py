#!/usr/bin/env python3
"""
测试 bar1.png 背景布局的脚本
"""

import sys
from pathlib import Path

# 添加项目路径到 sys.path
project_root = Path(__file__).parent / "WutheringWavesUID" / "WutheringWavesUID"
sys.path.insert(0, str(project_root))

def test_bar1_layout():
    """测试 bar1.png 背景布局"""
    try:
        print("🔍 测试图片资源加载...")
        
        # 测试路径
        texture_path = project_root / "wutheringwaves_rank" / "texture2d"
        bar1_path = texture_path / "bar1.png"
        
        print(f"纹理路径: {texture_path}")
        print(f"bar1.png 路径: {bar1_path}")
        
        # 检查文件是否存在
        if bar1_path.exists():
            print("✅ bar1.png 文件存在")
        else:
            print("❌ bar1.png 文件不存在")
            return False
        
        # 测试PIL导入和图片加载
        from PIL import Image
        print("✅ PIL 导入成功")
        
        # 加载图片
        bar1_img = Image.open(bar1_path)
        print(f"✅ bar1.png 加载成功，尺寸: {bar1_img.size}")
        
        # 测试图片调整
        target_size = (1200, 120)
        resized_img = bar1_img.resize(target_size, Image.Resampling.LANCZOS)
        print(f"✅ 图片尺寸调整成功: {bar1_img.size} -> {resized_img.size}")
        
        # 测试其他资源文件
        other_files = [
            "avatar_mask.png",
            "logo_small_2.png",
            "bar.png"
        ]
        
        for filename in other_files:
            file_path = texture_path / filename
            if file_path.exists():
                print(f"✅ {filename} 存在")
            else:
                print(f"⚠️ {filename} 不存在")
        
        print("\n📊 布局测试:")
        print("排名位置: (40, 25)")
        print("头像位置: (100, 20)")
        print("昵称位置: (200, 30)")
        print("UID位置: (200, 60)")
        print("平均出金: (420, 40)")
        print("平均UP: (550, 40)")
        print("总抽数: (680, 40)")
        print("运气标签: (820, 35)")
        
        print("\n🎨 视觉效果:")
        print("- 使用 bar1.png 作为背景")
        print("- 当前用户红色高亮覆盖层")
        print("- 数据使用 waves_font_34 字体")
        print("- 运气标签圆角设计")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_layout_positions():
    """测试布局位置是否合理"""
    print("\n📐 布局位置分析:")
    
    # 假设的 bar1.png 尺寸和条目高度
    bar_width = 1200
    bar_height = 120
    
    positions = {
        "排名": (40, 25),
        "头像": (100, 20),
        "昵称": (200, 30),
        "UID": (200, 60),
        "平均出金": (420, 40),
        "平均UP": (550, 40),
        "总抽数": (680, 40),
        "运气标签": (820, 35)
    }
    
    print(f"条目尺寸: {bar_width} x {bar_height}")
    print("\n元素位置:")
    
    for name, (x, y) in positions.items():
        x_percent = (x / bar_width) * 100
        y_percent = (y / bar_height) * 100
        print(f"{name:8}: ({x:3}, {y:2}) - X轴 {x_percent:5.1f}%, Y轴 {y_percent:5.1f}%")
    
    # 检查是否有重叠
    print("\n🔍 重叠检查:")
    overlap_found = False
    
    # 简单的重叠检测（假设每个元素占用一定空间）
    element_sizes = {
        "排名": (70, 70),
        "头像": (80, 80),
        "昵称": (120, 30),
        "UID": (120, 20),
        "平均出金": (80, 40),
        "平均UP": (80, 40),
        "总抽数": (80, 40),
        "运气标签": (60, 30)
    }
    
    for name1, (x1, y1) in positions.items():
        w1, h1 = element_sizes[name1]
        for name2, (x2, y2) in positions.items():
            if name1 >= name2:  # 避免重复检查
                continue
            w2, h2 = element_sizes[name2]
            
            # 检查矩形重叠
            if (x1 < x2 + w2 and x1 + w1 > x2 and 
                y1 < y2 + h2 and y1 + h1 > y2):
                print(f"⚠️ 可能重叠: {name1} 和 {name2}")
                overlap_found = True
    
    if not overlap_found:
        print("✅ 未发现明显重叠")
    
    return not overlap_found

if __name__ == "__main__":
    print("🧪 bar1.png 背景布局测试\n")
    
    # 测试资源加载
    resource_ok = test_bar1_layout()
    
    # 测试布局位置
    layout_ok = test_layout_positions()
    
    print(f"\n📋 测试结果:")
    print(f"资源加载: {'✅ 通过' if resource_ok else '❌ 失败'}")
    print(f"布局检查: {'✅ 通过' if layout_ok else '❌ 失败'}")
    
    if resource_ok and layout_ok:
        print("\n🎉 所有测试通过！bar1.png 背景布局应该可以正常工作！")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题！")
