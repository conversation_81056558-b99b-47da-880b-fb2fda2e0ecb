import asyncio
from pathlib import Path
from typing import List, Optional, Union

from PIL import Image, ImageDraw

from gsuid_core.bot import Bo<PERSON>
from gsuid_core.models import Event
from gsuid_core.utils.image.convert import convert_img
from gsuid_core.utils.image.image_tools import crop_center_img

from ..utils.cache import TimedCache
from ..utils.database.models import WavesGachaStats
from ..utils.fonts.waves_fonts import (
    waves_font_16,
    waves_font_18,
    waves_font_20,
    waves_font_24,
    waves_font_30,
    waves_font_34,
    waves_font_40,
    waves_font_44,
)
from ..utils.image import (
    GREY,
    RED,
    SPECIAL_GOLD,
    add_footer,
    get_qq_avatar,
    get_waves_bg,
)
from ..utils.util import hide_uid
from ..wutheringwaves_config import PREFIX, WutheringWavesConfig

TEXT_PATH = Path(__file__).parent / "texture2d"
avatar_mask = Image.open(TEXT_PATH / "avatar_mask.png")
logo_img = Image.open(TEXT_PATH / "logo_small_2.png")
bar1_img = Image.open(TEXT_PATH / "bar1.png")
pic_cache = TimedCache(86400, 200)


async def draw_gacha_rank_img(
    bot: Bot, ev: Event, rank_type: str
) -> Union[str, bytes]:
    """绘制抽卡排行榜图片
    
    Args:
        bot: 机器人实例
        ev: 事件对象
        rank_type: 排行类型 ("角色欧皇榜", "角色非酋榜", "武器欧皇榜", "武器非酋榜")
    """
    if not ev.group_id:
        return "请在群聊中使用此功能"
    
    # 获取群组抽卡统计数据
    gacha_stats = await WavesGachaStats.get_group_stats(ev.group_id, limit=100)
    
    if not gacha_stats:
        return f"群【{ev.group_id}】暂无抽卡统计数据，请先使用抽卡记录功能！"
    
    # 过滤有效数据并排序
    valid_stats = []
    for stat in gacha_stats:
        if rank_type in ["角色欧皇榜", "角色非酋榜"]:
            if stat.char_avg_gold is not None and stat.char_total_pulls > 0:
                valid_stats.append(stat)
        else:  # 武器排行
            if stat.weapon_avg_gold is not None and stat.weapon_total_pulls > 0:
                valid_stats.append(stat)
    
    if not valid_stats:
        pool_name = "角色池" if "角色" in rank_type else "武器池"
        return f"群【{ev.group_id}】暂无{pool_name}抽卡统计数据！"
    
    # 排序逻辑
    if rank_type in ["角色欧皇榜", "角色非酋榜"]:
        # 角色池：按平均出金排序
        reverse_order = rank_type == "角色非酋榜"  # 非酋榜从大到小
        valid_stats.sort(key=lambda x: x.char_avg_gold, reverse=reverse_order)
    else:
        # 武器池：按平均出金排序
        reverse_order = rank_type == "武器非酋榜"  # 非酋榜从大到小
        valid_stats.sort(key=lambda x: x.weapon_avg_gold, reverse=reverse_order)
    
    # 限制显示数量
    display_stats = valid_stats[:20]
    
    # 查找当前用户排名
    user_rank = None
    user_stat = None
    for i, stat in enumerate(valid_stats):
        if stat.user_id == ev.user_id:
            user_rank = i + 1
            user_stat = stat
            break
    
    # 如果用户排名超过20，添加到显示列表
    if user_rank and user_rank > 20 and user_stat:
        display_stats.append(user_stat)
    
    # 绘制图片
    return await draw_gacha_rank_card(ev, display_stats, rank_type, user_rank)


async def draw_gacha_rank_card(
    ev: Event,
    stats_list: List[WavesGachaStats],
    rank_type: str,
    user_rank: Optional[int] = None
) -> bytes:
    """绘制抽卡排行卡片"""

    total_num = len(stats_list)
    title_h = 280
    bar_h = 100
    margin = 20
    h = title_h + total_num * bar_h + margin * 2

    card_img = get_waves_bg(1000, h, "bg3")
    card_img_draw = ImageDraw.Draw(card_img)

    # 绘制标题区域
    title_bg = Image.new("RGBA", (1000, title_h), (0, 0, 0, 0))
    title_draw = ImageDraw.Draw(title_bg)

    # Logo
    title_bg.alpha_composite(logo_img.copy(), dest=(40, 30))

    # 主标题
    title_text = rank_type
    title_draw.text((500, 50), title_text, "white", waves_font_40, "mm")

    # 副标题
    pool_name = "角色精准调谐" if "角色" in rank_type else "武器精准调谐"
    sort_desc = "抽数越少越欧" if "欧皇" in rank_type else "抽数越多越非"
    desc_text = f"{pool_name} · {sort_desc}"
    title_draw.text((500, 90), desc_text, SPECIAL_GOLD, waves_font_20, "mm")

    # 计算统计数据并显示在标题区域
    if "角色" in rank_type:
        avg_values = [s.char_avg_gold for s in stats_list if s.char_avg_gold is not None]
        total_pulls_sum = sum(s.char_total_pulls for s in stats_list if s.char_total_pulls)
    else:
        avg_values = [s.weapon_avg_gold for s in stats_list if s.weapon_avg_gold is not None]
        total_pulls_sum = sum(s.weapon_total_pulls for s in stats_list if s.weapon_total_pulls)

    if avg_values:
        avg_gold_overall = sum(avg_values) / len(avg_values)
        best_luck = min(avg_values)
        worst_luck = max(avg_values)

        # 统计信息
        stats_text = f"群平均: {avg_gold_overall:.1f}抽  |  最欧: {best_luck:.1f}抽  |  最非: {worst_luck:.1f}抽  |  总抽数: {total_pulls_sum:,}抽"
        title_draw.text((500, 130), stats_text, GREY, waves_font_16, "mm")

    # 表头背景
    header_y = 170
    title_draw.rounded_rectangle([40, header_y, 960, header_y + 40], radius=8, fill=(255, 255, 255, 30))

    # 表头文字
    title_draw.text((80, header_y + 20), "排名", "white", waves_font_18, "mm")
    title_draw.text((200, header_y + 20), "用户", "white", waves_font_18, "mm")
    title_draw.text((400, header_y + 20), "平均出金", "white", waves_font_18, "mm")
    title_draw.text((550, header_y + 20), "平均UP", "white", waves_font_18, "mm")
    title_draw.text((700, header_y + 20), "总抽数", "white", waves_font_18, "mm")

    card_img.alpha_composite(title_bg, (0, margin))
    
    # 获取用户头像
    tasks = [get_user_avatar(ev, stat.user_id) for stat in stats_list]
    avatars = await asyncio.gather(*tasks)
    
    # 绘制排行条目
    for index, (stat, avatar) in enumerate(zip(stats_list, avatars)):
        y_pos = title_h + margin + index * bar_h

        # 创建条目背景
        is_current_user = stat.user_id == ev.user_id

        # 创建条目背景
        bar_bg = Image.new("RGBA", (960, bar_h - 10), (0, 0, 0, 0))
        bar_draw = ImageDraw.Draw(bar_bg)

        # 绘制条目背景和边框
        if is_current_user:
            # 当前用户使用特殊背景
            bar_draw.rounded_rectangle([0, 0, 960, bar_h - 10], radius=12, fill=(255, 215, 0, 30))
            bar_draw.rounded_rectangle([0, 0, 960, bar_h - 10], radius=12, outline=SPECIAL_GOLD + (255,), width=3)
        else:
            # 普通用户背景
            bar_draw.rounded_rectangle([0, 0, 960, bar_h - 10], radius=12, fill=(255, 255, 255, 20))
            bar_draw.rounded_rectangle([0, 0, 960, bar_h - 10], radius=12, outline=(255, 255, 255, 100), width=2)

        # 排名
        rank_num = index + 1
        if user_rank and rank_num > 20:
            rank_num = user_rank

        rank_color = get_rank_color(rank_num)
        # 如果是当前用户，使用特殊颜色
        if is_current_user and rank_num > 3:
            rank_color = SPECIAL_GOLD
        draw_rank_number(bar_bg, rank_num, rank_color, pos=(20, 15), is_current_user=is_current_user)

        # 用户头像
        avatar_pos = (120, 10)
        bar_bg.paste(avatar, avatar_pos, avatar)

        # 用户信息
        nickname = stat.nickname or "未知用户"
        uid_text = hide_uid(stat.uid)

        # 用户昵称 - 当前用户使用特殊颜色
        nickname_color = SPECIAL_GOLD if is_current_user else "white"
        uid_color = SPECIAL_GOLD if is_current_user else GREY

        bar_draw.text((220, 25), nickname[:10], nickname_color, waves_font_20, "lm")
        bar_draw.text((220, 50), uid_text, uid_color, waves_font_16, "lm")

        # 抽卡数据
        if "角色" in rank_type:
            avg_gold = stat.char_avg_gold or 0
            avg_up = stat.char_avg_up or 0
            total_pulls = stat.char_total_pulls or 0
        else:
            avg_gold = stat.weapon_avg_gold or 0
            avg_up = stat.weapon_avg_up or 0
            total_pulls = stat.weapon_total_pulls or 0

        # 数据显示区域 - 重新布局
        # 平均出金
        gold_color = SPECIAL_GOLD if avg_gold <= 60 else "white" if avg_gold <= 80 else RED
        bar_draw.text((380, 35), f"{avg_gold:.1f}", gold_color, waves_font_24, "mm")
        bar_draw.text((380, 55), "抽", GREY, waves_font_16, "mm")

        # 平均UP
        if avg_up > 0:
            up_color = SPECIAL_GOLD if avg_up <= 80 else "white"
            bar_draw.text((530, 35), f"{avg_up:.1f}", up_color, waves_font_24, "mm")
            bar_draw.text((530, 55), "抽", GREY, waves_font_16, "mm")
        else:
            bar_draw.text((530, 45), "暂无", GREY, waves_font_18, "mm")

        # 总抽数
        pulls_color = SPECIAL_GOLD if total_pulls >= 1000 else "white"
        bar_draw.text((680, 35), f"{total_pulls}", pulls_color, waves_font_24, "mm")
        bar_draw.text((680, 55), "总抽", GREY, waves_font_16, "mm")

        # 将条目贴到主画布上
        card_img.alpha_composite(bar_bg, (20, y_pos))



    # 添加页脚
    card_img = add_footer(card_img)
    card_img = await convert_img(card_img)

    return card_img


async def get_user_avatar(ev: Event, user_id: str) -> Image.Image:
    """获取用户头像"""
    avatar_size = 80

    if ev.bot_id == "onebot":
        if WutheringWavesConfig.get_config("QQPicCache").data:
            pic = pic_cache.get(user_id)
            if not pic:
                pic = await get_qq_avatar(user_id, size=avatar_size)
                pic_cache.set(user_id, pic)
        else:
            pic = await get_qq_avatar(user_id, size=avatar_size)

        pic_temp = crop_center_img(pic, avatar_size, avatar_size)

        # 创建带边框的头像
        img = Image.new("RGBA", (avatar_size, avatar_size), (0, 0, 0, 0))
        mask_pic_temp = avatar_mask.resize((avatar_size, avatar_size))
        img.paste(pic_temp, (0, 0), mask_pic_temp)

        return img
    else:
        # 非QQ平台使用默认头像
        default_avatar = Image.new("RGBA", (avatar_size, avatar_size), (100, 100, 100, 255))
        default_draw = ImageDraw.Draw(default_avatar)
        default_draw.ellipse([10, 10, avatar_size-10, avatar_size-10], fill=(150, 150, 150, 255))
        default_draw.text((avatar_size//2, avatar_size//2), "?", "white", waves_font_30, "mm")
        return default_avatar


def get_rank_color(rank: int) -> tuple:
    """获取排名颜色"""
    if rank == 1:
        return (255, 215, 0)  # 金色
    elif rank == 2:
        return (192, 192, 192)  # 银色
    elif rank == 3:
        return (205, 127, 50)  # 铜色
    else:
        return (100, 100, 100)  # 灰色





def draw_rank_number(img: Image.Image, rank: int, color: tuple, pos: tuple = (10, 20), is_current_user: bool = False):
    """绘制排名数字"""
    # 根据排名调整大小
    if rank <= 3:
        size = 70
        font_size = waves_font_30
    elif rank <= 10:
        size = 60
        font_size = waves_font_24
    else:
        size = 50
        font_size = waves_font_20

    rank_bg = Image.new("RGBA", (size, size), (0, 0, 0, 0))
    rank_draw = ImageDraw.Draw(rank_bg)

    # 绘制圆形背景
    margin = 3
    rank_draw.ellipse([margin, margin, size-margin, size-margin], fill=color + (255,))

    # 添加光泽效果（前三名或当前用户）
    if rank <= 3 or is_current_user:
        # 高光效果
        highlight_size = size // 3
        rank_draw.ellipse([size//4, size//6, size//4 + highlight_size, size//6 + highlight_size],
                         fill=(255, 255, 255, 100))

    # 绘制排名数字
    rank_text = str(rank) if rank <= 999 else "999+"
    text_color = "white" if rank > 3 else "black" if rank == 1 else "white"
    rank_draw.text((size//2, size//2), rank_text, text_color, font_size, "mm")

    # 添加边框 - 当前用户使用特殊边框
    if is_current_user:
        border_color = SPECIAL_GOLD + (255,)
        border_width = 3
    elif rank <= 3:
        border_color = (255, 255, 255, 150)
        border_width = 2
    else:
        border_color = (100, 100, 100, 100)
        border_width = 2

    rank_draw.ellipse([0, 0, size-1, size-1], outline=border_color, width=border_width)

    img.alpha_composite(rank_bg, pos)

