import asyncio
from pathlib import Path
from typing import List, Optional, Union

from PIL import Image, ImageDraw

from gsuid_core.bot import Bo<PERSON>
from gsuid_core.models import Event
from gsuid_core.utils.image.convert import convert_img
from gsuid_core.utils.image.image_tools import crop_center_img

from ..utils.cache import TimedCache
from ..utils.database.models import WavesGachaStats
from ..utils.fonts.waves_fonts import (
    waves_font_16,
    waves_font_18,
    waves_font_20,
    waves_font_24,
    waves_font_30,
    waves_font_34,
    waves_font_40,
    waves_font_44,
)
from ..utils.image import (
    GREY,
    RED,
    SPECIAL_GOLD,
    add_footer,
    get_qq_avatar,
    get_waves_bg,
)
from ..utils.util import hide_uid
from ..wutheringwaves_config import PREFIX, WutheringWavesConfig

TEXT_PATH = Path(__file__).parent / "texture2d"
avatar_mask = Image.open(TEXT_PATH / "avatar_mask.png")
logo_img = Image.open(TEXT_PATH / "logo_small_2.png")
pic_cache = TimedCache(86400, 200)


async def draw_gacha_rank_img(
    bot: Bot, ev: Event, rank_type: str
) -> Union[str, bytes]:
    """绘制抽卡排行榜图片
    
    Args:
        bot: 机器人实例
        ev: 事件对象
        rank_type: 排行类型 ("角色欧皇榜", "角色非酋榜", "武器欧皇榜", "武器非酋榜")
    """
    if not ev.group_id:
        return "请在群聊中使用此功能"
    
    # 获取群组抽卡统计数据
    gacha_stats = await WavesGachaStats.get_group_stats(ev.group_id, limit=100)
    
    if not gacha_stats:
        return f"群【{ev.group_id}】暂无抽卡统计数据，请先使用抽卡记录功能！"
    
    # 过滤有效数据并排序
    valid_stats = []
    for stat in gacha_stats:
        if rank_type in ["角色欧皇榜", "角色非酋榜"]:
            if stat.char_avg_gold is not None and stat.char_total_pulls > 0:
                valid_stats.append(stat)
        else:  # 武器排行
            if stat.weapon_avg_gold is not None and stat.weapon_total_pulls > 0:
                valid_stats.append(stat)
    
    if not valid_stats:
        pool_name = "角色池" if "角色" in rank_type else "武器池"
        return f"群【{ev.group_id}】暂无{pool_name}抽卡统计数据！"
    
    # 排序逻辑
    if rank_type in ["角色欧皇榜", "角色非酋榜"]:
        # 角色池：按平均出金排序
        reverse_order = rank_type == "角色非酋榜"  # 非酋榜从大到小
        valid_stats.sort(key=lambda x: x.char_avg_gold, reverse=reverse_order)
    else:
        # 武器池：按平均出金排序
        reverse_order = rank_type == "武器非酋榜"  # 非酋榜从大到小
        valid_stats.sort(key=lambda x: x.weapon_avg_gold, reverse=reverse_order)
    
    # 限制显示数量
    display_stats = valid_stats[:20]
    
    # 查找当前用户排名
    user_rank = None
    user_stat = None
    for i, stat in enumerate(valid_stats):
        if stat.user_id == ev.user_id:
            user_rank = i + 1
            user_stat = stat
            break
    
    # 如果用户排名超过20，添加到显示列表
    if user_rank and user_rank > 20 and user_stat:
        display_stats.append(user_stat)
    
    # 绘制图片
    return await draw_gacha_rank_card(ev, display_stats, rank_type, user_rank)


async def draw_gacha_rank_card(
    ev: Event, 
    stats_list: List[WavesGachaStats], 
    rank_type: str,
    user_rank: Optional[int] = None
) -> bytes:
    """绘制抽卡排行卡片"""
    
    total_num = len(stats_list)
    title_h = 400
    bar_h = 100
    h = title_h + total_num * bar_h + 80
    
    card_img = get_waves_bg(1000, h, "bg3")
    card_img_draw = ImageDraw.Draw(card_img)
    
    # 绘制标题
    title_bg = Image.new("RGBA", (1000, title_h), (0, 0, 0, 0))
    title_draw = ImageDraw.Draw(title_bg)
    
    # Logo
    title_bg.alpha_composite(logo_img.copy(), dest=(50, 50))
    
    # 标题文字
    title_text = rank_type
    title_draw.text((500, 150), title_text, "white", waves_font_44, "mm")
    
    # 说明文字
    pool_name = "角色精准调谐" if "角色" in rank_type else "武器精准调谐"
    desc_text = f"基于{pool_name}平均出金抽数排序"
    title_draw.text((500, 200), desc_text, SPECIAL_GOLD, waves_font_20, "mm")
    
    # 入榜条件
    condition_text = f"入榜条件：使用【{PREFIX}抽卡记录】命令后自动统计"
    title_draw.text((50, 320), condition_text, GREY, waves_font_16, "lm")
    
    card_img.alpha_composite(title_bg, (0, 0))
    
    # 获取用户头像
    tasks = [get_user_avatar(ev, stat.user_id) for stat in stats_list]
    avatars = await asyncio.gather(*tasks)
    
    # 绘制排行条目
    for index, (stat, avatar) in enumerate(zip(stats_list, avatars)):
        y_pos = title_h + index * bar_h
        
        # 创建条目背景
        bar_bg = Image.new("RGBA", (1000, bar_h), (255, 255, 255, 20))
        bar_draw = ImageDraw.Draw(bar_bg)
        
        # 排名
        rank_num = index + 1
        if user_rank and rank_num > 20:
            rank_num = user_rank
            
        rank_color = get_rank_color(rank_num)
        draw_rank_number(bar_bg, rank_num, rank_color)
        
        # 用户头像
        bar_bg.paste(avatar, (80, 10), avatar)
        
        # 用户信息
        nickname = stat.nickname or "未知用户"
        uid_text = hide_uid(stat.uid)
        
        # 高亮当前用户
        text_color = RED if stat.user_id == ev.user_id else "white"
        
        bar_draw.text((200, 25), nickname, text_color, waves_font_24, "lm")
        bar_draw.text((200, 55), f"UID: {uid_text}", GREY, waves_font_18, "lm")
        
        # 抽卡数据
        if "角色" in rank_type:
            avg_gold = stat.char_avg_gold or 0
            avg_up = stat.char_avg_up or 0
            total_pulls = stat.char_total_pulls or 0
        else:
            avg_gold = stat.weapon_avg_gold or 0
            avg_up = stat.weapon_avg_up or 0
            total_pulls = stat.weapon_total_pulls or 0
        
        # 平均出金
        bar_draw.text((450, 25), f"平均出金: {avg_gold:.1f}抽", SPECIAL_GOLD, waves_font_20, "lm")
        
        # 平均UP
        if avg_up > 0:
            bar_draw.text((450, 55), f"平均UP: {avg_up:.1f}抽", "white", waves_font_18, "lm")
        else:
            bar_draw.text((450, 55), "平均UP: 暂无", GREY, waves_font_18, "lm")
        
        # 总抽数
        bar_draw.text((700, 40), f"总抽数: {total_pulls}", "white", waves_font_20, "lm")
        
        card_img.alpha_composite(bar_bg, (0, y_pos))
    
    # 添加页脚
    card_img = add_footer(card_img)
    card_img = await convert_img(card_img)
    
    return card_img


async def get_user_avatar(ev: Event, user_id: str) -> Image.Image:
    """获取用户头像"""
    if ev.bot_id == "onebot":
        if WutheringWavesConfig.get_config("QQPicCache").data:
            pic = pic_cache.get(user_id)
            if not pic:
                pic = await get_qq_avatar(user_id, size=80)
                pic_cache.set(user_id, pic)
        else:
            pic = await get_qq_avatar(user_id, size=80)
        
        pic_temp = crop_center_img(pic, 80, 80)
        img = Image.new("RGBA", (80, 80))
        mask_pic_temp = avatar_mask.resize((80, 80))
        img.paste(pic_temp, (0, 0), mask_pic_temp)
        return img
    else:
        # 非QQ平台使用默认头像
        default_avatar = Image.new("RGBA", (80, 80), (100, 100, 100, 255))
        return default_avatar


def get_rank_color(rank: int) -> tuple:
    """获取排名颜色"""
    if rank == 1:
        return (255, 215, 0)  # 金色
    elif rank == 2:
        return (192, 192, 192)  # 银色
    elif rank == 3:
        return (205, 127, 50)  # 铜色
    else:
        return (100, 100, 100)  # 灰色


def draw_rank_number(img: Image.Image, rank: int, color: tuple):
    """绘制排名数字"""
    rank_bg = Image.new("RGBA", (60, 60), color + (200,))
    rank_draw = ImageDraw.Draw(rank_bg)
    
    # 绘制圆形背景
    rank_draw.ellipse([5, 5, 55, 55], fill=color + (255,))
    
    # 绘制排名数字
    rank_text = str(rank) if rank <= 999 else "999+"
    font_size = waves_font_24 if rank <= 99 else waves_font_18
    rank_draw.text((30, 30), rank_text, "white", font_size, "mm")
    
    img.alpha_composite(rank_bg, (10, 20))
