#!/usr/bin/env python3
"""
测试抽卡排行功能导入的脚本
"""

import sys
from pathlib import Path

# 添加项目路径到 sys.path
project_root = Path(__file__).parent / "WutheringWavesUID" / "WutheringWavesUID"
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有导入"""
    try:
        print("🔍 测试基础导入...")
        import asyncio
        from pathlib import Path
        from typing import List, Optional, Union
        print("✅ 基础模块导入成功")
        
        print("🔍 测试PIL导入...")
        from PIL import Image, ImageDraw
        print("✅ PIL模块导入成功")
        
        print("🔍 测试字体导入...")
        from utils.fonts.waves_fonts import (
            waves_font_14,
            waves_font_16,
            waves_font_18,
            waves_font_20,
            waves_font_24,
            waves_font_30,
            waves_font_34,
            waves_font_40,
            waves_font_44,
        )
        print("✅ 字体模块导入成功")
        
        print("🔍 测试工具导入...")
        from utils.image import (
            GREY,
            RED,
            SPECIAL_GOLD,
            add_footer,
            get_qq_avatar,
            get_waves_bg,
        )
        print("✅ 图像工具导入成功")
        
        print("🔍 测试数据库模型导入...")
        from utils.database.models import WavesGachaStats
        print("✅ 数据库模型导入成功")
        
        print("🔍 测试配置导入...")
        from wutheringwaves_config import PREFIX, WutheringWavesConfig
        print("✅ 配置模块导入成功")
        
        print("🔍 测试其他工具导入...")
        from utils.cache import TimedCache
        from utils.util import hide_uid
        print("✅ 其他工具导入成功")
        
        print("\n🎉 所有导入测试通过！")
        
        # 测试字体对象
        print(f"\n📝 字体测试:")
        print(f"waves_font_14: {waves_font_14}")
        print(f"waves_font_16: {waves_font_16}")
        print(f"waves_font_20: {waves_font_20}")
        
        # 测试颜色常量
        print(f"\n🎨 颜色测试:")
        print(f"GREY: {GREY}")
        print(f"RED: {RED}")
        print(f"SPECIAL_GOLD: {SPECIAL_GOLD}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print(f"错误详情: {e.__class__.__name__}: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        print(f"错误详情: {e.__class__.__name__}: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n✅ 所有测试通过，抽卡排行功能应该可以正常工作！")
    else:
        print("\n❌ 测试失败，请检查导入问题！")
