{
  "python.languageServer": "None",
  "editor.formatOnSave": true,
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },
  "python.autoComplete.extraPaths": [
    "${workspaceFolder}/../../../../"
  ],
  "isort.args": [
    "--profile",
    "black"
  ],
  "basedpyright.analysis.extraPaths": [
    "${workspaceFolder}/../../../"
  ],
}