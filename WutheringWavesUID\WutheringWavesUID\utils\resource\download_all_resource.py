from gsuid_core.utils.download_resource.download_core import download_all_file

from .RESOURCE_PATH import (
    AVA<PERSON>R_PATH,
    JIEXING_GUIDE_PATH,
    JINLINGZI_GUIDE_PATH,
    MATE<PERSON><PERSON>_PATH,
    <PERSON><PERSON>AL<PERSON>YNE_GUIDE_PATH,
    <PERSON><PERSON><PERSON><PERSON>_PATH,
    R<PERSON><PERSON>_DETAIL_CHAINS_PATH,
    ROLE_DETAIL_SKILL_PATH,
    ROLE_PILE_PATH,
    SHARE_BG_PATH,
    WEAPON_PATH,
    WUH<PERSON>_GUIDE_PATH,
    XIAOYANG_GUIDE_PATH,
    XMU_GUIDE_PATH,
)


async def download_all_resource():
    await download_all_file(
        "WutheringWavesUID",
        {
            "resource/avatar": A<PERSON><PERSON>R_PATH,
            "resource/weapon": WEAPON_PATH,
            "resource/role_pile": ROLE_PILE_PATH,
            "resource/role_detail/skill": ROLE_DETAIL_SKILL_PATH,
            "resource/role_detail/chains": ROLE_DETAIL_CHAINS_PATH,
            "resource/share": <PERSON>AR<PERSON>_BG_PATH,
            "resource/phantom": <PERSON><PERSON><PERSON><PERSON>_PATH,
            "resource/material": MATE<PERSON><PERSON>_PATH,
            "resource/guide/XMu": XMU_GUIDE_PATH,
            "resource/guide/Moealkyne": MOEALKYNE_GUIDE_PATH,
            "resource/guide/JinLingZi": JINLINGZI_GUIDE_PATH,
            "resource/guide/JieXing": JIEXING_GUIDE_PATH,
            "resource/guide/XiaoYang": XIAOYANG_GUIDE_PATH,
            "resource/guide/WuHen": WUHEN_GUIDE_PATH,
        },
    )
