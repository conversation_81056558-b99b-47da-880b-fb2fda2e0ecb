import re

from gsuid_core.bot import Bo<PERSON>
from gsuid_core.models import Event
from gsuid_core.sv import SV
from .darw_rank_card import draw_rank_img
from .draw_all_rank_card import draw_all_rank_card
from .draw_gacha_rank import draw_gacha_rank_img

sv_waves_rank_list = SV("ww角色排行")
sv_waves_rank_all_list = SV("ww角色总排行", priority=1)
sv_waves_gacha_rank = SV("ww抽卡排行", priority=1)


@sv_waves_rank_list.on_regex("^[\u4e00-\u9fa5]+(?:排行|排名)$", block=True)
async def send_rank_card(bot: Bot, ev: Event):
    # 正则表达式
    match = re.search(r"(?P<char>[\u4e00-\u9fa5]+)(?:排行|排名)", ev.raw_text)
    if not match:
        return
    ev.regex_dict = match.groupdict()
    char = match.group("char")

    if not ev.group_id:
        return await bot.send("请在群聊中使用")

    if not char:
        return

    rank_type = "伤害"
    if "评分" in char:
        rank_type = "评分"
    char = char.replace("伤害", "").replace("评分", "")

    im = await draw_rank_img(bot, ev, char, rank_type)

    if isinstance(im, str):
        at_sender = True if ev.group_id else False
        await bot.send(im, at_sender)
    if isinstance(im, bytes):
        await bot.send(im)


@sv_waves_rank_all_list.on_regex(
    "^[\u4e00-\u9fa5]+(?:总排行|总排名)(\d+)?$", block=True
)
async def send_all_rank_card(bot: Bot, ev: Event):
    # 正则表达式
    match = re.search(
        r"(?P<char>[\u4e00-\u9fa5]+)(?:总排行|总排名)(?P<pages>(\d+))?",
        ev.raw_text,
    )
    if not match:
        return
    ev.regex_dict = match.groupdict()
    char = match.group("char")
    pages = match.group("pages")

    if not char:
        return

    if pages:
        pages = int(pages)
    else:
        pages = 1

    if pages > 5:
        pages = 5
    elif pages < 1:
        pages = 1

    rank_type = "伤害"
    if "评分" in char:
        rank_type = "评分"
    char = char.replace("伤害", "").replace("评分", "")

    im = await draw_all_rank_card(bot, ev, char, rank_type, pages)

    if isinstance(im, str):
        at_sender = True if ev.group_id else False
        await bot.send(im, at_sender)
    if isinstance(im, bytes):
        await bot.send(im)


@sv_waves_gacha_rank.on_regex("^(角色|武器)(欧皇榜|非酋榜|欧非榜)$", block=True)
async def send_gacha_rank_card(bot: Bot, ev: Event):
    """抽卡欧非排行榜"""
    match = re.search(r"^(角色|武器)(欧皇榜|非酋榜|欧非榜)$", ev.raw_text)
    if not match:
        return

    pool_type = match.group(1)  # 角色 或 武器
    rank_type_raw = match.group(2)  # 欧皇榜、非酋榜、欧非榜

    if not ev.group_id:
        return await bot.send("请在群聊中使用此功能")

    # 处理欧非榜（显示欧皇榜）
    if rank_type_raw == "欧非榜":
        rank_type_raw = "欧皇榜"

    rank_type = f"{pool_type}{rank_type_raw}"

    im = await draw_gacha_rank_img(bot, ev, rank_type)

    if isinstance(im, str):
        at_sender = True if ev.group_id else False
        await bot.send(im, at_sender)
    elif isinstance(im, bytes):
        await bot.send(im)
