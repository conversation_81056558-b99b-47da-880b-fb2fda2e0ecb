# 抽卡欧非排行功能

## 功能概述

新增了抽卡欧非排行功能，用户可以通过发送特定命令查看群内的抽卡运气排行榜。

## 支持的命令

### 基本命令格式
- `角色欧皇榜` - 查看角色池欧皇排行（平均出金抽数从小到大）
- `角色非酋榜` - 查看角色池非酋排行（平均出金抽数从大到小）
- `角色欧非榜` - 查看角色池欧皇排行（等同于角色欧皇榜）
- `武器欧皇榜` - 查看武器池欧皇排行（平均出金抽数从小到大）
- `武器非酋榜` - 查看武器池非酋排行（平均出金抽数从大到小）
- `武器欧非榜` - 查看武器池欧皇排行（等同于武器欧皇榜）

## 功能特点

### 1. 数据来源
- 基于 `WavesGachaStats` 表中的统计数据
- 只显示当前群组内用户的数据
- 需要用户先使用过抽卡记录功能才会有数据

### 2. 排序规则
- **欧皇榜**：按平均出金抽数从小到大排序（抽数越少越欧）
- **非酋榜**：按平均出金抽数从大到小排序（抽数越多越非）

### 3. 显示内容
每个排行条目包含以下信息：
- **排名**：1-3名有特殊颜色和光泽效果（金银铜），带圆形边框
- **用户头像**：QQ头像（支持缓存），带白色边框
- **游戏昵称**：从游戏API获取的真实昵称
- **UID**：隐藏部分数字的UID
- **平均出金**：该池子的平均出金抽数，带颜色标识和背景
- **平均UP**：该池子的平均UP抽数（如果有），带颜色标识
- **总抽数**：该池子的总抽卡次数
- **运气标签**：根据平均出金显示"超欧"、"小欧"、"平均"、"非酋"

### 4. 用户体验
- 最多显示20条记录
- 当前用户会高亮显示（红色背景边框）
- 如果当前用户排名超过20，会额外显示在列表末尾
- 支持QQ头像缓存，提升加载速度
- 表格式布局，清晰的表头和分割线
- 渐变背景和视觉效果
- 统计信息页脚显示群平均、最欧、最非等数据

## 技术实现

### 1. 文件结构
```
wutheringwaves_rank/
├── __init__.py              # 命令注册
├── draw_gacha_rank.py       # 抽卡排行绘制逻辑
├── darw_rank_card.py        # 原有角色排行
└── texture2d/               # 图片资源
```

### 2. 核心函数
- `draw_gacha_rank_img()`: 主要入口函数，处理命令和数据获取
- `draw_gacha_rank_card()`: 绘制排行榜图片
- `get_user_avatar()`: 获取用户头像
- `get_rank_color()`: 获取排名颜色
- `draw_rank_number()`: 绘制排名数字

### 3. 数据过滤
- 只显示有有效抽卡数据的用户
- 角色池：需要 `char_avg_gold` 不为空且 `char_total_pulls > 0`
- 武器池：需要 `weapon_avg_gold` 不为空且 `weapon_total_pulls > 0`

## 使用示例

### 用户操作
1. 用户先使用 `抽卡记录` 命令，系统自动保存统计数据
2. 在群聊中发送 `角色欧皇榜` 查看角色池欧皇排行
3. 发送 `武器非酋榜` 查看武器池非酋排行

### 系统响应
- 如果群内无数据：提示用户先使用抽卡记录功能
- 如果有数据：生成排行榜图片并发送
- 图片包含完整的排行信息和说明文字

## 错误处理

### 1. 数据验证
- 检查群组ID是否存在
- 验证数据库中是否有该群的统计数据
- 过滤无效的抽卡记录

### 2. 异常处理
- 头像获取失败时使用默认头像
- 数据库查询异常时返回友好提示
- 图片生成失败时返回错误信息

## 配置选项

### QQ头像缓存
- 配置项：`QQPicCache`
- 功能：缓存用户QQ头像，减少重复请求
- 缓存时间：24小时
- 缓存数量：最多200个

## 注意事项

1. **数据依赖**：需要用户先使用抽卡记录功能生成统计数据
2. **群聊限制**：只能在群聊中使用，私聊无法获取群组数据
3. **数据实时性**：排行榜基于最后一次查看抽卡记录时的数据
4. **隐私保护**：UID会部分隐藏，保护用户隐私

## 布局优化特点

### 1. 视觉设计
- **表格式布局**：清晰的列对齐，易于阅读
- **渐变背景**：标题区域使用渐变效果
- **颜色编码**：不同数值范围使用不同颜色
- **圆角设计**：现代化的圆角元素

### 2. 排名显示
- **前三名特效**：金银铜色彩 + 光泽效果
- **动态大小**：排名越高，图标越大
- **边框装饰**：白色边框突出显示

### 3. 数据可视化
- **运气标签**：
  - 超欧（≤50抽）：金色
  - 小欧（≤70抽）：白色
  - 平均（≤90抽）：灰色
  - 非酋（>90抽）：红色
- **背景色块**：重要数据带有背景高亮
- **统计页脚**：显示群体统计信息

### 4. 用户识别
- **当前用户高亮**：红色背景边框
- **头像边框**：白色圆形边框
- **昵称显示**：支持长昵称截断

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加新的排序规则
- 支持添加更多显示字段
- 可以扩展到其他类型的统计排行
- 布局组件化，易于调整和美化
