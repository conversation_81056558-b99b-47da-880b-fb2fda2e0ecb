#!/usr/bin/env python3
"""
测试抽卡统计功能的脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径到 sys.path
project_root = Path(__file__).parent / "WutheringWavesUID" / "WutheringWavesUID"
sys.path.insert(0, str(project_root))

async def test_gacha_stats():
    """测试抽卡统计数据库功能"""
    try:
        from utils.database.models import WavesGachaStats
        
        print("✅ 成功导入 WavesGachaStats 模型")
        
        # 测试数据
        test_data = {
            "user_id": "123456789",
            "bot_id": "test_bot",
            "group_id": "987654321",
            "nickname": "漂泊者",  # 游戏内昵称
            "uid": "100000001",
            "char_avg_gold": 65.5,
            "char_avg_up": 80.2,
            "char_total_pulls": 150,
            "weapon_avg_gold": 55.8,
            "weapon_avg_up": 70.1,
            "weapon_total_pulls": 120,
        }
        
        print("✅ 测试数据准备完成")
        print(f"测试数据: {test_data}")
        
        # 测试模型字段
        print("\n📊 数据库字段说明:")
        print("- user_id: 用户QQ号")
        print("- bot_id: 机器人ID")
        print("- group_id: 群号")
        print("- nickname: 游戏内昵称")
        print("- uid: 鸣潮UID")
        print("- char_avg_gold: 角色池平均出金抽数")
        print("- char_avg_up: 角色池平均UP抽数")
        print("- char_total_pulls: 角色池总抽数")
        print("- weapon_avg_gold: 武器池平均出金抽数")
        print("- weapon_avg_up: 武器池平均UP抽数")
        print("- weapon_total_pulls: 武器池总抽数")
        print("- created_at: 创建时间")
        print("- updated_at: 更新时间")
        
        print("\n✅ 数据库模型定义正确")
        print("✅ 功能说明: 当用户发送抽卡记录命令时，会自动保存统计数据到数据库")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_gacha_stats())
