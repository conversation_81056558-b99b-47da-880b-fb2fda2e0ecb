#!/usr/bin/env python3
"""
测试抽卡排行功能的脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径到 sys.path
project_root = Path(__file__).parent / "WutheringWavesUID" / "WutheringWavesUID"
sys.path.insert(0, str(project_root))

async def test_gacha_rank():
    """测试抽卡排行功能"""
    try:
        # 测试导入
        from wutheringwaves_rank.draw_gacha_rank import draw_gacha_rank_img
        from utils.database.models import WavesGachaStats
        
        print("✅ 成功导入抽卡排行模块")
        
        # 测试支持的命令
        supported_commands = [
            "角色欧皇榜",
            "角色非酋榜", 
            "角色欧非榜",
            "武器欧皇榜",
            "武器非酋榜",
            "武器欧非榜"
        ]
        
        print("\n📋 支持的命令:")
        for cmd in supported_commands:
            print(f"  - {cmd}")
        
        print("\n📊 功能说明:")
        print("1. 欧皇榜：按平均出金抽数从小到大排序（越少越欧）")
        print("2. 非酋榜：按平均出金抽数从大到小排序（越多越非）")
        print("3. 显示内容：排名、头像、昵称、UID、平均出金、平均UP、总抽数")
        print("4. 最多显示20条记录")
        print("5. 当前用户会高亮显示")
        
        print("\n🎯 使用流程:")
        print("1. 用户发送 '抽卡记录' 命令 → 系统自动保存统计数据")
        print("2. 在群聊中发送 '角色欧皇榜' → 查看角色池排行")
        print("3. 发送 '武器非酋榜' → 查看武器池排行")
        
        print("\n✅ 抽卡排行功能测试完成")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_gacha_rank())
