from typing import Any, Dict, List, Optional, Type, TypeVar
from datetime import datetime

from sqlalchemy import delete, null, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import and_, or_
from sqlmodel import Field, col, select, SQLModel

from gsuid_core.utils.database.base_models import (
    Bind,
    Push,
    User,
    with_session,
)
from gsuid_core.utils.database.startup import exec_list
from gsuid_core.webconsole.mount_app import GsAdminModel, PageSchema, site

exec_list.extend(
    [
        'ALTER TABLE WavesUser ADD COLUMN platform TEXT DEFAULT ""',
        'ALTER TABLE WavesUser ADD COLUMN stamina_bg_value TEXT DEFAULT ""',
        'ALTER TABLE WavesUser ADD COLUMN bbs_sign_switch TEXT DEFAULT "off"',
        'ALTER TABLE WavesUser ADD COLUMN bat TEXT DEFAULT ""',
        'ALTER TABLE WavesUser ADD COLUMN did TEXT DEFAULT ""',
    ]
)

T_WavesBind = TypeVar("T_WavesBind", bound="WavesBind")
T_WavesUser = TypeVar("T_WavesUser", bound="WavesUser")
T_WavesGachaStats = TypeVar("T_WavesGachaStats", bound="WavesGachaStats")


class WavesBind(Bind, table=True):
    __table_args__: Dict[str, Any] = {"extend_existing": True}
    uid: Optional[str] = Field(default=None, title="鸣潮UID")

    @classmethod
    @with_session
    async def get_group_all_uid(
        cls: Type[T_WavesBind], session: AsyncSession, group_id: Optional[str] = None
    ):
        """根据传入`group_id`获取该群号下所有绑定`uid`列表"""
        result = await session.scalars(
            select(cls).where(col(cls.group_id).contains(group_id))
        )
        return result.all()

    @classmethod
    async def insert_waves_uid(
        cls: Type[T_WavesBind],
        user_id: str,
        bot_id: str,
        uid: str,
        group_id: Optional[str] = None,
        lenth_limit: Optional[int] = None,
        is_digit: Optional[bool] = True,
        game_name: Optional[str] = None,
    ) -> int:
        if lenth_limit:
            if len(uid) != lenth_limit:
                return -1

        if is_digit:
            if not uid.isdigit():
                return -3
        if not uid:
            return -1

        # 第一次绑定
        if not await cls.bind_exists(user_id, bot_id):
            code = await cls.insert_data(
                user_id=user_id,
                bot_id=bot_id,
                **{"uid": uid, "group_id": group_id},
            )
            return code

        result = await cls.select_data(user_id, bot_id)
        # await user_bind_cache.set(user_id, result)

        uid_list = result.uid.split("_") if result and result.uid else []
        uid_list = [i for i in uid_list if i] if uid_list else []

        # 已经绑定了该UID
        res = 0 if uid not in uid_list else -2

        # 强制更新库表
        force_update = False
        if uid not in uid_list:
            uid_list.append(uid)
            force_update = True
        new_uid = "_".join(uid_list)

        group_list = result.group_id.split("_") if result and result.group_id else []
        group_list = [i for i in group_list if i] if group_list else []

        if group_id and group_id not in group_list:
            group_list.append(group_id)
            force_update = True
        new_group_id = "_".join(group_list)

        if force_update:
            await cls.update_data(
                user_id=user_id,
                bot_id=bot_id,
                **{"uid": new_uid, "group_id": new_group_id},
            )
        return res


class WavesUser(User, table=True):
    __table_args__: Dict[str, Any] = {"extend_existing": True}
    cookie: str = Field(default="", title="Cookie")
    uid: str = Field(default=None, title="鸣潮UID")
    record_id: Optional[str] = Field(default=None, title="鸣潮记录ID")
    platform: str = Field(default="", title="ck平台")
    stamina_bg_value: str = Field(default="", title="体力背景")
    bbs_sign_switch: str = Field(default="off", title="自动社区签到")
    bat: str = Field(default="", title="bat")
    did: str = Field(default="", title="did")

    @classmethod
    @with_session
    async def mark_cookie_invalid(
        cls: Type[T_WavesUser], session: AsyncSession, uid: str, cookie: str, mark: str
    ):
        sql = (
            update(cls)
            .where(col(cls.uid) == uid)
            .where(col(cls.cookie) == cookie)
            .values(status=mark)
        )
        await session.execute(sql)
        return True

    @classmethod
    @with_session
    async def select_cookie(
        cls: Type[T_WavesUser],
        session: AsyncSession,
        uid: str,
        user_id: str,
        bot_id: str,
    ) -> Optional[str]:
        sql = select(cls).where(
            cls.user_id == user_id,
            cls.uid == uid,
            cls.bot_id == bot_id,
        )
        result = await session.execute(sql)
        data = result.scalars().all()
        return data[0].cookie if data else None

    @classmethod
    @with_session
    async def select_waves_user(
        cls: Type[T_WavesUser],
        session: AsyncSession,
        uid: str,
        user_id: str,
        bot_id: str,
    ) -> Optional[T_WavesUser]:
        sql = select(cls).where(
            cls.user_id == user_id,
            cls.uid == uid,
            cls.bot_id == bot_id,
        )
        result = await session.execute(sql)
        data = result.scalars().all()
        return data[0] if data else None

    @classmethod
    @with_session
    async def select_user_cookie_uids(
        cls: Type[T_WavesUser],
        session: AsyncSession,
        user_id: str,
    ) -> List[str]:
        sql = select(cls).where(
            and_(
                col(cls.user_id) == user_id,
                col(cls.cookie) != null(),
                col(cls.cookie) != "",
                or_(col(cls.status) == null(), col(cls.status) == ""),
            )
        )
        result = await session.execute(sql)
        data = result.scalars().all()
        return [i.uid for i in data] if data else []

    @classmethod
    @with_session
    async def select_data_by_cookie(
        cls: Type[T_WavesUser], session: AsyncSession, cookie: str
    ) -> Optional[T_WavesUser]:
        sql = select(cls).where(cls.cookie == cookie)
        result = await session.execute(sql)
        data = result.scalars().all()
        return data[0] if data else None

    @classmethod
    @with_session
    async def select_data_by_cookie_and_uid(
        cls: Type[T_WavesUser], session: AsyncSession, cookie: str, uid: str
    ) -> Optional[T_WavesUser]:
        sql = select(cls).where(cls.cookie == cookie, cls.uid == uid)
        result = await session.execute(sql)
        data = result.scalars().all()
        return data[0] if data else None

    @classmethod
    async def get_user_by_attr(
        cls: Type[T_WavesUser],
        user_id: str,
        bot_id: str,
        attr_key: str,
        attr_value: str,
    ) -> Optional[Any]:
        user_list = await cls.select_data_list(user_id=user_id, bot_id=bot_id)
        if not user_list:
            return None
        for user in user_list:
            if getattr(user, attr_key) != attr_value:
                continue
            return user

    @classmethod
    @with_session
    async def get_waves_all_user(
        cls: Type[T_WavesUser], session: AsyncSession
    ) -> List[T_WavesUser]:
        """获取所有有效用户"""
        sql = select(cls).where(
            and_(
                or_(col(cls.status) == null(), col(cls.status) == ""),
                col(cls.cookie) != null(),
                col(cls.cookie) != "",
            )
        )

        result = await session.execute(sql)
        data = result.scalars().all()
        return list(data)

    @classmethod
    @with_session
    async def delete_all_invalid_cookie(cls, session: AsyncSession):
        """删除所有无效缓存"""
        sql = delete(cls).where(
            or_(col(cls.status) == "无效", col(cls.cookie) == ""),
        )
        result = await session.execute(sql)
        return result.rowcount

    @classmethod
    @with_session
    async def delete_cookie(
        cls,
        session: AsyncSession,
        uid: str,
        user_id: str,
        bot_id: str,
    ):
        sql = delete(cls).where(
            and_(
                col(cls.user_id) == user_id,
                col(cls.uid) == uid,
                col(cls.bot_id) == bot_id,
            )
        )
        result = await session.execute(sql)
        return result.rowcount


class WavesPush(Push, table=True):
    __table_args__: Dict[str, Any] = {"extend_existing": True}
    bot_id: str = Field(title="平台")
    uid: str = Field(default=None, title="鸣潮UID")
    resin_push: Optional[str] = Field(
        title="体力推送",
        default="off",
        schema_extra={"json_schema_extra": {"hint": "ww开启体力推送"}},
    )
    resin_value: Optional[int] = Field(title="体力阈值", default=180)
    resin_is_push: Optional[str] = Field(title="体力是否已推送", default="off")


class WavesGachaStats(SQLModel, table=True):
    """抽卡统计表"""
    __table_args__: Dict[str, Any] = {"extend_existing": True}

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: str = Field(title="用户QQ号")
    bot_id: str = Field(title="机器人ID")
    group_id: Optional[str] = Field(default=None, title="群号")
    nickname: Optional[str] = Field(default=None, title="用户昵称")
    uid: str = Field(title="鸣潮UID")

    # 角色精准调谐统计
    char_avg_gold: Optional[float] = Field(default=None, title="角色池平均出金")
    char_avg_up: Optional[float] = Field(default=None, title="角色池平均UP")
    char_total_pulls: int = Field(default=0, title="角色池总抽数")

    # 武器精准调谐统计
    weapon_avg_gold: Optional[float] = Field(default=None, title="武器池平均出金")
    weapon_avg_up: Optional[float] = Field(default=None, title="武器池平均UP")
    weapon_total_pulls: int = Field(default=0, title="武器池总抽数")

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, title="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, title="更新时间")

    @classmethod
    @with_session
    async def save_or_update_stats(
        cls: Type[T_WavesGachaStats],
        session: AsyncSession,
        user_id: str,
        bot_id: str,
        group_id: Optional[str],
        nickname: Optional[str],
        uid: str,
        char_avg_gold: Optional[float],
        char_avg_up: Optional[float],
        char_total_pulls: int,
        weapon_avg_gold: Optional[float],
        weapon_avg_up: Optional[float],
        weapon_total_pulls: int,
    ) -> T_WavesGachaStats:
        """保存或更新抽卡统计数据"""
        # 查找是否已存在记录
        sql = select(cls).where(
            cls.user_id == user_id,
            cls.bot_id == bot_id,
            cls.uid == uid,
        )
        result = await session.execute(sql)
        existing_record = result.scalars().first()

        if existing_record:
            # 更新现有记录
            existing_record.group_id = group_id
            existing_record.nickname = nickname
            existing_record.char_avg_gold = char_avg_gold
            existing_record.char_avg_up = char_avg_up
            existing_record.char_total_pulls = char_total_pulls
            existing_record.weapon_avg_gold = weapon_avg_gold
            existing_record.weapon_avg_up = weapon_avg_up
            existing_record.weapon_total_pulls = weapon_total_pulls
            existing_record.updated_at = datetime.now()
            session.add(existing_record)
            await session.commit()
            await session.refresh(existing_record)
            return existing_record
        else:
            # 创建新记录
            new_record = cls(
                user_id=user_id,
                bot_id=bot_id,
                group_id=group_id,
                nickname=nickname,
                uid=uid,
                char_avg_gold=char_avg_gold,
                char_avg_up=char_avg_up,
                char_total_pulls=char_total_pulls,
                weapon_avg_gold=weapon_avg_gold,
                weapon_avg_up=weapon_avg_up,
                weapon_total_pulls=weapon_total_pulls,
            )
            session.add(new_record)
            await session.commit()
            await session.refresh(new_record)
            return new_record

    @classmethod
    @with_session
    async def get_user_stats(
        cls: Type[T_WavesGachaStats],
        session: AsyncSession,
        user_id: str,
        bot_id: str,
        uid: str,
    ) -> Optional[T_WavesGachaStats]:
        """获取用户抽卡统计"""
        sql = select(cls).where(
            cls.user_id == user_id,
            cls.bot_id == bot_id,
            cls.uid == uid,
        )
        result = await session.execute(sql)
        return result.scalars().first()

    @classmethod
    @with_session
    async def get_group_stats(
        cls: Type[T_WavesGachaStats],
        session: AsyncSession,
        group_id: str,
        limit: int = 50,
    ) -> List[T_WavesGachaStats]:
        """获取群组抽卡统计排行"""
        sql = (
            select(cls)
            .where(cls.group_id == group_id)
            .order_by(cls.updated_at.desc())
            .limit(limit)
        )
        result = await session.execute(sql)
        return list(result.scalars().all())

    @classmethod
    @with_session
    async def get_all_stats(
        cls: Type[T_WavesGachaStats],
        session: AsyncSession,
        limit: int = 100,
    ) -> List[T_WavesGachaStats]:
        """获取所有抽卡统计数据"""
        sql = select(cls).order_by(cls.updated_at.desc()).limit(limit)
        result = await session.execute(sql)
        return list(result.scalars().all())


@site.register_admin
class WavesBindAdmin(GsAdminModel):
    pk_name = "id"
    page_schema = PageSchema(
        label="鸣潮绑定管理",
        icon="fa fa-users",
    )  # type: ignore

    # 配置管理模型
    model = WavesBind


@site.register_admin
class WavesUserAdmin(GsAdminModel):
    pk_name = "id"
    page_schema = PageSchema(
        label="鸣潮用户管理",
        icon="fa fa-users",
    )  # type: ignore

    # 配置管理模型
    model = WavesUser


@site.register_admin
class WavesPushAdmin(GsAdminModel):
    pk_name = "id"
    page_schema = PageSchema(label="鸣潮推送管理", icon="fa fa-bullhorn")  # type: ignore

    # 配置管理模型
    model = WavesPush


@site.register_admin
class WavesGachaStatsAdmin(GsAdminModel):
    pk_name = "id"
    page_schema = PageSchema(label="鸣潮抽卡统计管理", icon="fa fa-chart-bar")  # type: ignore

    # 配置管理模型
    model = WavesGachaStats
